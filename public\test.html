<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste da API - Trabalho Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .token-display {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🚀 Teste da API - Trabalho Backend</h1>
    
    <div class="container">
        <h2>👤 Criar Usuário</h2>
        <input type="text" id="usuario" placeholder="Usuário" value="joao123">
        <input type="password" id="senha" placeholder="Senha" value="123456">
        <button onclick="criarUsuario()">Criar Usuário</button>
        <div id="resultUsuario" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🔑 Login</h2>
        <input type="text" id="loginUsuario" placeholder="Usuário" value="joao123">
        <input type="password" id="loginSenha" placeholder="Senha" value="123456">
        <button onclick="fazerLogin()">Fazer Login</button>
        <div id="resultLogin" class="result" style="display:none;"></div>
        <div id="tokenDisplay" class="token-display" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>👥 Clientes (Rota Protegida)</h2>
        <button onclick="listarClientes()">Listar Clientes</button>
        <button onclick="criarCliente()" class="success">Criar Cliente</button>
        <div id="resultClientes" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>📦 Produtos (Rota Pública)</h2>
        <button onclick="listarProdutos()">Listar Produtos</button>
        <button onclick="criarProduto()" class="success">Criar Produto</button>
        <div id="resultProdutos" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🚪 Logout</h2>
        <button onclick="fazerLogout()" class="danger">Fazer Logout</button>
        <div id="resultLogout" class="result" style="display:none;"></div>
    </div>

    <script>
        let token = '';

        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function criarUsuario() {
            try {
                const response = await fetch('http://localhost:3000/usuarios', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        usuario: document.getElementById('usuario').value,
                        senha: document.getElementById('senha').value
                    })
                });
                const data = await response.json();
                showResult('resultUsuario', data);
            } catch (error) {
                showResult('resultUsuario', {error: error.message});
            }
        }

        async function fazerLogin() {
            try {
                const response = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        usuario: document.getElementById('loginUsuario').value,
                        senha: document.getElementById('loginSenha').value
                    })
                });
                const data = await response.json();
                showResult('resultLogin', data);
                
                if (data.token) {
                    token = data.token;
                    const tokenDisplay = document.getElementById('tokenDisplay');
                    tokenDisplay.style.display = 'block';
                    tokenDisplay.textContent = `Token JWT: ${token}`;
                }
            } catch (error) {
                showResult('resultLogin', {error: error.message});
            }
        }

        async function listarClientes() {
            try {
                const response = await fetch('http://localhost:3000/clientes', {
                    headers: {'Authorization': `Bearer ${token}`}
                });
                const data = await response.json();
                showResult('resultClientes', data);
            } catch (error) {
                showResult('resultClientes', {error: error.message});
            }
        }

        async function criarCliente() {
            try {
                const response = await fetch('http://localhost:3000/clientes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        nome: 'Maria',
                        sobrenome: 'Santos',
                        email: '<EMAIL>',
                        idade: 25,
                        data_nascimento: '1998-05-15'
                    })
                });
                const data = await response.json();
                showResult('resultClientes', data);
            } catch (error) {
                showResult('resultClientes', {error: error.message});
            }
        }

        async function listarProdutos() {
            try {
                const response = await fetch('http://localhost:3000/produtos');
                const data = await response.json();
                showResult('resultProdutos', data);
            } catch (error) {
                showResult('resultProdutos', {error: error.message});
            }
        }

        async function criarProduto() {
            try {
                const response = await fetch('http://localhost:3000/produtos', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        nome: 'Produto Teste',
                        descricao: 'Descrição do produto teste',
                        preco: 99.99
                    })
                });
                const data = await response.json();
                showResult('resultProdutos', data);
            } catch (error) {
                showResult('resultProdutos', {error: error.message});
            }
        }

        async function fazerLogout() {
            try {
                const response = await fetch('http://localhost:3000/auth/logout', {
                    method: 'POST',
                    headers: {'Authorization': `Bearer ${token}`}
                });
                const data = await response.json();
                showResult('resultLogout', data);
                
                if (response.ok) {
                    token = '';
                    document.getElementById('tokenDisplay').style.display = 'none';
                }
            } catch (error) {
                showResult('resultLogout', {error: error.message});
            }
        }
    </script>
</body>
</html> 