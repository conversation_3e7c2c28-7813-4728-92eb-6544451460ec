extends layout

block content
  h1 Clientes
  
  if clientes && clientes.length > 0
    table
      thead
        tr
          th ID
          th Nome
          th Sobrenome
          th Email
          th <PERSON><PERSON>
          th Ações
      tbody
        each cliente in clientes
          tr
            td= cliente.id
            td= cliente.nome
            td= cliente.sobrenome
            td= cliente.email
            td= cliente.idade
            td
              a(href='/clientes/' + cliente.id) Visualizar
  else
    p Nenhum cliente encontrado.
  
  h2 Adicionar Novo Cliente
  form(action='/clientes', method='POST')
    div
      label(for='nome') Nome:
      input(type='text', name='nome', required)
    
    div
      label(for='sobrenome') Sobrenome:
      input(type='text', name='sobrenome', required)
    
    div
      label(for='email') Email:
      input(type='email', name='email', required)
    
    div
      label(for='idade') Idade:
      input(type='number', name='idade', required, min='0')
    
    div
      button(type='submit') Adicionar Cliente
