{"name": "desafio-node-api", "version": "1.0.0", "description": "Desafio de API REST com Node.js e MySQL", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest --runInBand --detectOpenHandles --forceExit", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@supabase/supabase-js": "^2.49.4", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "chalk": "^4.1.2", "cookie-parser": "^1.4.7", "cross-fetch": "^4.1.0", "debug": "^4.4.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "http-errors": "^2.0.0", "jade": "^1.11.0", "jsonwebtoken": "^9.0.2", "method-override": "^3.0.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^9.24.0", "eslint-config-google": "^0.14.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.1.1"}}